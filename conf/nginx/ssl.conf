server {
    listen 443 ssl;
    http2 on;
    server_name dauthau.local daugianet.local;

    ssl_certificate /etc/nginx/conf.d/server.crt;
	ssl_certificate_key /etc/nginx/conf.d/server.key;

    location / {
        proxy_pass https://dauthau_info:8443;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Forwarded-Port 8443;

        proxy_buffer_size          32k;
        proxy_buffers              8 64k;
        proxy_busy_buffers_size    128k;
        proxy_max_temp_file_size   0;
    }
}

server {
    listen 443 ssl;
    http2 on;
    server_name dauthaunet.local;

    ssl_certificate /etc/nginx/conf.d/server.crt;
	ssl_certificate_key /etc/nginx/conf.d/server.key;

    location / {
        proxy_pass https://dauthau_net:8443;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Forwarded-Port 8443;
		
		proxy_buffer_size          32k;
		proxy_buffers              8 64k;
		proxy_busy_buffers_size    128k;
		proxy_max_temp_file_size   0;		
    }
}

server {
    listen 443 ssl;
    http2 on;
    server_name id.dauthau.local;

    ssl_certificate /etc/nginx/conf.d/server.crt;
	ssl_certificate_key /etc/nginx/conf.d/server.key;

    location / {
        proxy_pass https://dauthau_id:8443;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Forwarded-Port 8443;
        client_max_body_size 60M;

        proxy_buffer_size          32k;
        proxy_buffers              8 64k;
        proxy_busy_buffers_size    128k;
        proxy_max_temp_file_size   0;
    }
}

server {
    listen 443 ssl;
    http2 on;
    server_name db.dauthau.local;

    ssl_certificate /etc/nginx/conf.d/server.crt;
	ssl_certificate_key /etc/nginx/conf.d/server.key;

    location / {
        client_max_body_size 100M;
        proxy_pass http://dauthau_phpmyadmin;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        proxy_buffer_size          32k;
        proxy_buffers              8 64k;
        proxy_busy_buffers_size    128k;
        proxy_max_temp_file_size   0;
    }
}

server {
    listen 443 ssl;
    http2 on;
    server_name sso.dauthau.local;

    ssl_certificate /etc/nginx/conf.d/server.crt;
	ssl_certificate_key /etc/nginx/conf.d/server.key;

    location / {
        proxy_pass https://dauthau_sso:8443;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Forwarded-Port 8443;
    }
}

server {
    listen 443 ssl;
    http2 on;
    server_name api.dauthau.local;

    ssl_certificate /etc/nginx/conf.d/server.crt;
	ssl_certificate_key /etc/nginx/conf.d/server.key;

    location / {
        proxy_pass https://dauthau_api:8443;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Forwarded-Port 8443;
    }
}

server {
    listen 443 ssl;
    http2 on;
    server_name crmprivate.dauthau.local;

    ssl_certificate /etc/nginx/conf.d/server.crt;
	ssl_certificate_key /etc/nginx/conf.d/server.key;

    location / {
        proxy_pass https://dauthau_crmprivate:8443;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Forwarded-Port 8443;
    }
}

server {
    listen 443 ssl;
    http2 on;
    server_name x1.dauthau.local;

    ssl_certificate /etc/nginx/conf.d/server.crt;
	ssl_certificate_key /etc/nginx/conf.d/server.key;

    location / {
        proxy_pass https://dauthau_crontabdb:8443;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Forwarded-Port 8443;
    }
}

server {
    listen 443 ssl;
    http2 on;
    server_name file1.dauthau.local;

    ssl_certificate /etc/nginx/conf.d/server.crt;
	ssl_certificate_key /etc/nginx/conf.d/server.key;

    location / {
        proxy_pass https://dauthau_fileserver:8443;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Forwarded-Port 8443;
    }
}

# server {
#     listen 443 ssl;
#     http2 on;
#     server_name idapi.dauthau.local;

#     ssl_certificate /etc/nginx/conf.d/server.crt;
# 	ssl_certificate_key /etc/nginx/conf.d/server.key;

#     location / {
#         proxy_pass https://dauthau_idapi:8443;
#         proxy_set_header Host $host;
#         proxy_set_header X-Real-IP $remote_addr;
#         proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
#         proxy_set_header X-Forwarded-Proto $scheme;
#         proxy_set_header X-Forwarded-Port 8443;
#     }
# }
