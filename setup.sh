#!/bin/bash

SOURCE="${BASH_SOURCE[0]}"
while [ -h "$SOURCE" ]; do
  TARGET="$(readlink "$SOURCE")"
  if [[ $TARGET == /* ]]; then
    SOURCE="$TARGET"
  else
    DIR="$( dirname "$SOURCE" )"
    SOURCE="$DIR/$TARGET"
  fi
done
DIR="$( cd -P "$( dirname "$SOURCE" )" >/dev/null 2>&1 && pwd )"
cd "$DIR/"
DIR_PATH=$PWD

# Dừng và xóa các container nếu đang chạy
docker-compose down

# Hỏi dùng ssh hay https cho kho code vinades.org
GIT_HTTPS=1
while true; do
  read -p "Dùng SSH hay HTTPS cho vinades.org [y:SSH / n(mặc định):HTTPS]? " yn
  if [[ "$yn" = "y" || "$yn" = "Y" ]] ; then
    GIT_HTTPS=0
    break
  elif [[ "$yn" = "n" || "$yn" = "N" || "$yn" = "" ]] ; then
    break
  else
    echo "Vui lòng nhập y hoặc n hoặc để trống"
  fi
done
if [[ "$GIT_HTTPS" -eq 1 ]] ; then echo "Dùng HTTPS cho kho code" ; else echo "Dùng SSH cho kho code" ; fi

# Hỏi có reset tất cả code đang có hay không
DELETE_EXISTS=0
while true; do
  read -p "Xóa hết code đang tồn tại nếu có [y/n(mặc định)]? " yn
  if [[ "$yn" = "y" || "$yn" = "Y" ]] ; then
    DELETE_EXISTS=1
    break
  elif [[ "$yn" = "n" || "$yn" = "N" || "$yn" = "" ]] ; then
    break
  else
    echo "Vui lòng nhập y hoặc n hoặc để trống"
  fi
done
if [[ "$DELETE_EXISTS" -eq 1 ]] ; then
  echo "Xóa các kho code đang có"
  rm -rf "$DIR_PATH/src"
  mkdir -p "$DIR_PATH/src"
else
  echo "Giữ lại các kho code đang có"
fi

# Tạo thư mục cần thiết
mkdir -p "$DIR_PATH/_docker"
mkdir -p "$DIR_PATH/db"
mkdir -p "$DIR_PATH/src/dauthau.info"
mkdir -p "$DIR_PATH/src/api.dauthau.info"
mkdir -p "$DIR_PATH/src/id.dauthau.net"
mkdir -p "$DIR_PATH/src/idapi.dauthau.net"
mkdir -p "$DIR_PATH/src/crmprivate.dauthau.net"
mkdir -p "$DIR_PATH/src/sso.dauthau.net"
mkdir -p "$DIR_PATH/src/dauthau.net"
mkdir -p "$DIR_PATH/src/fileserver"
mkdir -p "$DIR_PATH/src/dauthau-crawls"
mkdir -p "$DIR_PATH/src/dauthau.info-crontab-db"
mkdir -p "$DIR_PATH/src/dauthau.net.crawls"
mkdir -p "$DIR_PATH/src/dauthau.net.private"
#mkdir -p "$DIR_PATH/src/idapi.dauthau.net"
mkdir -p "$DIR_PATH/src/sendmail-aws"

NUM_CLONED=0
# Cài dauthau.info
if [ ! -f "$DIR_PATH/src/dauthau.info/config.php" ] ; then
  cd "$DIR_PATH/src/dauthau.info"
  if [[ "$GIT_HTTPS" -eq 1 ]] ; then
    git clone https://vinades.org/dauthau/dauthau.info.git ./
  else
    git clone ssh://***************:1706/dauthau/dauthau.info.git ./
  fi
  cp "vinades/docker/config.php" "src/config.php"
  cp "vinades/docker/config_global.php" "src/data/config/config_global.php"
  cd "src/dev"
  sass theme.dauthau/scss/dauthau.responsive.scss:../themes/dauthau/css/dauthau.responsive.css theme.dauthau/scss/dauthau.non-responsive.scss:../themes/dauthau/css/dauthau.non-responsive.css --style compressed
  NUM_CLONED=$((NUM_CLONED + 1))
fi
# Cài dauthau.net
if [ ! -f "$DIR_PATH/src/dauthau.net/config.php" ] ; then
  cd "$DIR_PATH/src/dauthau.net"
  if [[ "$GIT_HTTPS" -eq 1 ]] ; then
    git clone https://vinades.org/dauthau/dauthau.net.git ./
  else
    git clone ssh://***************:1706/dauthau/dauthau.net.git ./
  fi
  cp "vinades/docker/config.php" "src/config.php"
  cp "vinades/docker/config_global.php" "src/data/config/config_global.php"
  cd "src/dev"
  echo -n "Build variable SASS to LESS"

  sass2less theme.dauthau/scss/_variables.scss > less/variables.less
  sass2less theme.dauthau/scss/_variables_dark.scss > less/variables_dark.less
  sass2less theme.dauthau/scss/_variables_red.scss > less/variables_red.less
  sass2less theme.dauthau/scss/_variables_home.scss > less/variables_home.less

  echo " > OK"
  echo -n "Build LESS to bootstrap"

  lessc less/bootstrap.less libs/bootstrap/bootstrap.css
  lessc less/bootstrap.non-responsive.less libs/bootstrap/bootstrap.non-responsive.css

  lessc less/bootstrap.red.less libs/bootstrap/bootstrap.red.css
  lessc less/bootstrap.red.non-responsive.less libs/bootstrap/bootstrap.red.non-responsive.css

  lessc less/bootstrap.dark.less libs/bootstrap/bootstrap.dark.css
  lessc less/bootstrap.dark.non-responsive.less libs/bootstrap/bootstrap.dark.non-responsive.css

  lessc less/bootstrap.home.less libs/bootstrap/bootstrap.home.css
  lessc less/bootstrap.home.non-responsive.less libs/bootstrap/bootstrap.home.non-responsive.css

  echo " > OK"
  echo "Build sass to CSS"
  sass theme.dauthau/scss/dauthau.responsive.scss:../themes/dauthau/css/dauthau.responsive.css theme.dauthau/scss/dauthau.red.responsive.scss:../themes/dauthau/css/dauthau.red.responsive.css theme.dauthau/scss/dauthau.dark.responsive.scss:../themes/dauthau/css/dauthau.dark.responsive.css theme.dauthau/scss/dauthau.home.responsive.scss:../themes/dauthau/css/dauthau.home.responsive.css theme.dauthau/scss/dauthau.non-responsive.scss:../themes/dauthau/css/dauthau.non-responsive.css theme.dauthau/scss/dauthau.red.non-responsive.scss:../themes/dauthau/css/dauthau.red.non-responsive.css theme.dauthau/scss/dauthau.dark.non-responsive.scss:../themes/dauthau/css/dauthau.dark.non-responsive.css theme.dauthau/scss/dauthau.home.non-responsive.scss:../themes/dauthau/css/dauthau.home.non-responsive.css --style compressed
  NUM_CLONED=$((NUM_CLONED + 1))
fi
# Cài fileserver cho DauThau.Net
if [ ! -f "$DIR_PATH/src/fileserver/config.php" ] ; then
  cd "$DIR_PATH/src/fileserver"
  if [[ "$GIT_HTTPS" -eq 1 ]] ; then
    git clone https://vinades.org/dauthau/fileserver.git ./
  else
    git clone ssh://***************:1706/dauthau/fileserver.git ./
  fi
  cp "vinades/docker/config.php" "config.php"
  cp "vinades/docker/config_global.php" "data/config/config_global.php"
  NUM_CLONED=$((NUM_CLONED + 1))
fi
# Cài id.dauthau.net
if [ ! -f "$DIR_PATH/src/id.dauthau.net/config.php" ] ; then
  cd "$DIR_PATH/src/id.dauthau.net"
  if [[ "$GIT_HTTPS" -eq 1 ]] ; then
    git clone https://vinades.org/dauthau/id.dauthau.net.git ./
  else
    git clone ssh://***************:1706/dauthau/id.dauthau.net.git ./
  fi
  cp "vinades/docker/config.php" "src/config.php"
  cp "vinades/docker/config_global.php" "src/data/config/config_global.php"
  cd "src/dev"
  sass theme.dauthau/scss/dauthau.responsive.scss:../themes/dauthau/css/dauthau.responsive.css theme.dauthau/scss/dauthau.non-responsive.scss:../themes/dauthau/css/dauthau.non-responsive.css --style compressed
  NUM_CLONED=$((NUM_CLONED + 1))
fi
# Cài sso.dauthau.net
if [ ! -f "$DIR_PATH/src/sso.dauthau.net/config.php" ] ; then
  cd "$DIR_PATH/src/sso.dauthau.net"
  if [[ "$GIT_HTTPS" -eq 1 ]] ; then
    git clone https://vinades.org/dauthau/sso.dauthau.net.git ./
  else
    git clone ssh://***************:1706/dauthau/sso.dauthau.net.git ./
  fi
  cp "vinades/config_docker.php" "config.php"
  NUM_CLONED=$((NUM_CLONED + 1))
fi
# Cài api.dauthau.info
if [ ! -f "$DIR_PATH/src/api.dauthau.info/config.php" ] ; then
  cd "$DIR_PATH/src/api.dauthau.info"
  if [[ "$GIT_HTTPS" -eq 1 ]] ; then
    git clone https://vinades.org/dauthau/api.dauthau.info.git ./
  else
    git clone ssh://***************:1706/dauthau/api.dauthau.info.git ./
  fi
  cp "vinades/docker/config.php" "src/config.php"
  cp "vinades/docker/config_global.php" "src/data/config/config_global.php"
  NUM_CLONED=$((NUM_CLONED + 1))
fi
# Cài crmprivate.dauthau.net
if [ ! -f "$DIR_PATH/src/crmprivate.dauthau.net/private/includes/config.php" ] ; then
  cd "$DIR_PATH/src/crmprivate.dauthau.net"
  if [[ "$GIT_HTTPS" -eq 1 ]] ; then
    git clone https://vinades.org/dauthau/crmprivate.dauthau.net.git ./
  else
    git clone ssh://***************:1706/dauthau/crmprivate.dauthau.net.git ./
  fi
  cp "vinades/config_docker.php" "private/includes/config.php"
  NUM_CLONED=$((NUM_CLONED + 1))
fi
# Cài crontab-db
if [ ! -f "$DIR_PATH/src/dauthau.info-crontab-db/private/includes/config.php" ] ; then
  cd "$DIR_PATH/src/dauthau.info-crontab-db"
  if [[ "$GIT_HTTPS" -eq 1 ]] ; then
    git clone https://vinades.org/dauthau/dauthau.info-crontab-db.git ./
  else
    git clone ssh://***************:1706/dauthau/dauthau.info-crontab-db.git ./
  fi
  cp "vinades/config_docker.php" "private/includes/config.php"
  NUM_CLONED=$((NUM_CLONED + 1))
fi
# Cài crawls
if [ ! -f "$DIR_PATH/src/dauthau-crawls/config.php" ] ; then
  cd "$DIR_PATH/src/dauthau-crawls"
  if [[ "$GIT_HTTPS" -eq 1 ]] ; then
    git clone https://vinades.org/dauthau/dauthau-crawls.git ./
  else
    git clone ssh://***************:1706/dauthau/dauthau-crawls.git ./
  fi
  cp "vinades/config_docker.php" "config.php"
  NUM_CLONED=$((NUM_CLONED + 1))
fi
# Cài sendmail-aws
if [ ! -f "$DIR_PATH/src/sendmail-aws/includes/config.php" ] ; then
  cd "$DIR_PATH/src/sendmail-aws"
  if [[ "$GIT_HTTPS" -eq 1 ]] ; then
    git clone https://vinades.org/dauthau/sendmail-aws.git ./
  else
    git clone ssh://***************:1706/dauthau/sendmail-aws.git ./
  fi
  cp "vinades/config_docker.php" "includes/config.php"
  NUM_CLONED=$((NUM_CLONED + 1))
fi
# Cài dauthau.net.private
if [ ! -f "$DIR_PATH/src/dauthau.net.private/config.php" ] ; then
  cd "$DIR_PATH/src/dauthau.net.private"
  if [[ "$GIT_HTTPS" -eq 1 ]] ; then
    git clone https://vinades.org/dauthau/dauthau.net.private.git ./
  else
    git clone ssh://***************:1706/dauthau/dauthau.net.private.git ./
  fi
  cp "vinades/config_docker.php" "config.php"
  NUM_CLONED=$((NUM_CLONED + 1))
fi
# Cài dauthau.net.crawls
if [ ! -f "$DIR_PATH/src/dauthau.net.crawls/includes/config.php" ] ; then
  cd "$DIR_PATH/src/dauthau.net.crawls"
  if [[ "$GIT_HTTPS" -eq 1 ]] ; then
    git clone https://vinades.org/dauthau/dauthau.net.crawls.git ./
  else
    git clone ssh://***************:1706/dauthau/dauthau.net.crawls.git ./
  fi
  cp "vinades/config_docker.php" "includes/config.php"
  NUM_CLONED=$((NUM_CLONED + 1))
fi

# Cài idapi.dauthau.net
if [ ! -f "$DIR_PATH/src/idapi.dauthau.net/src/libs/config.go" ] ; then
  cd "$DIR_PATH/src/idapi.dauthau.net"
  if [[ "$GIT_HTTPS" -eq 1 ]] ; then
    git clone https://vinades.org/dauthau/idapi.dauthau.net.git ./
  else
    git clone ssh://***************:1706/dauthau/idapi.dauthau.net.git ./
  fi
  docker run -v $DIR_PATH/src/idapi.dauthau.net:/root -ti --rm golang:alpine sh -c "apk add bash && \
    cd /root/src && \
    cp -f libs/config.go libs/config.go.release && \
    sed -i 's/^var AppScheme = \"http\"/var AppScheme = \"https\"/' libs/config.go.release
    sed -i 's/^var AppPort = \"8081\"/var AppPort = \"8443\"/' libs/config.go.release && \
    sed -i 's/^var AppDomain = \"idapi\.dauthau\.vinades\.my\"/var AppDomain = \"idapi.dauthau.local\"/' libs/config.go.release && \
    sed -i 's|^var ClientAllowed = \[\]string{\"http://dauthau\.vinades\.my\"}|var ClientAllowed = []string{\"https://dauthau.local:8443\"}|' libs/config.go.release && \
    sed -i 's/^var DbHost = \"id_dbhost\"/var DbHost = \"dauthau_db\"/' libs/config.go.release && \
    sed -i 's|^var SSLCertFile = \"\"|var SSLCertFile = \"/root/ssl.crt\"|' libs/config.go.release && \
    sed -i 's|^var SSLKeyFile = \"\"|var SSLKeyFile = \"/root/ssl.key\"|' libs/config.go.release && \
    rm -f go.mod && \
    rm -f go.sum && go mod init idapi.dauthau.net && \
    go get github.com/jmoiron/sqlx && go get github.com/go-sql-driver/mysql && go get github.com/gin-contrib/location && go get github.com/gin-contrib/cors && go get -u github.com/gin-gonic/gin && \
    cd /root && \
    bash -c './build.release.sh'"
  mv -f "$DIR_PATH/src/idapi.dauthau.net/idapi.release" "$DIR_PATH/conf/golang/idapi.release"
fi

if [[ $NUM_CLONED -gt 0 ]]; then
  if ( (uname -s) 2>&1 ) | grep 'Linux' ; then
    find "$DIR_PATH/src" -name '.git' -type d -exec bash -c 'git config --global --add safe.directory ${0%/.git}' {} \;
  fi
fi
cd "$DIR_PATH"

# Xóa các image custom đã tạo hay không
while true; do
  read -p "Xóa các docker image custom đã tạo [y/n(mặc định)]? " yn
  if [[ "$yn" = "y" || "$yn" = "y" ]] ; then
    echo "Xóa các image hstdt-*"
    for Repository in $(docker images --format '{{.Repository}}') ; do
      if [[ "$Repository" =~ ^hstdt- ]] ; then
        docker image rm -f "$Repository"
      fi
    done
    break
  elif [[ "$yn" = "n" || "$yn" = "N" || "$yn" = "" ]] ; then
    echo "Giữ lại các image hstdt-* nếu có"
    break
  else
    echo "Vui lòng nhập y hoặc n hoặc để trống"
  fi
done

# Hỏi dùng database có sẵn hay không
REIMPORT_DB=0
if [ ! -d "$DIR_PATH/_docker/mysql/dauthau_2018" ] ; then
  # Không tồn tại thư mục DB thì luôn tạo mới DB
  REIMPORT_DB=1
else
  while true; do
    read -p "Sử dụng các database đang có [y(mặc định)/n]? " yn
    if [[ "$yn" = "y" || "$yn" = "y" || "$yn" = "" ]] ; then
      REIMPORT_DB=0
      break
    elif [[ "$yn" = "n" || "$yn" = "N" ]] ; then
      REIMPORT_DB=1
      break
    else
      echo "Vui lòng nhập y hoặc n hoặc để trống"
    fi
  done
fi

if [[ "$REIMPORT_DB" -eq 1 ]] ; then
  echo "Xóa toàn bộ database tạo lại"
  rm -rf "$DIR_PATH/_docker"
else
  echo "Dùng các database sẵn có (nếu tồn tại)"
fi

# Tạo các thư mục chứa data mysql, es, kafka, zookeeper
mkdir -p "$DIR_PATH/_docker/mysql"
mkdir -p "$DIR_PATH/_docker/es_dt"
mkdir -p "$DIR_PATH/_docker/es_dtnet"
mkdir -p "$DIR_PATH/_docker/zookeeper"
mkdir -p "$DIR_PATH/_docker/kafka"

if [ -f "$DIR_PATH/conf/php82/.env" ] ; then
  rm -f "$DIR_PATH/conf/php82/.env"
fi
cp "$DIR_PATH/.env" "$DIR_PATH/conf/php82/.env"

docker-compose up -d

# Chờ MariaDB chạy hoàn tất
attempt=0
DB_READY=0
while [ $attempt -le 59 ]; do
  attempt=$(( $attempt + 1 ))
  echo "Đợi mariadb sẵn sàng (lần $attempt)..."
  result=$( (docker logs dauthau_db) 2>&1 )
  if grep -q 'MariaDB init process done. Ready for start up' <<< $result ; then
    echo "MariaDB sẵn sàng!"
    DB_READY=1
    break
  fi
  if grep -q 'MariaDB upgrade not required' <<< $result ; then
    echo "MariaDB sẵn sàng!"
    DB_READY=1
    break
  fi
  sleep 2
done
if [[ ! "$DB_READY" -eq 1 ]]; then
  echo "Không khởi chạy MariaDB thành công, vui lòng kiểm tra lại"
  exit
fi

# Chờ nginx chạy hoàn tất
attempt=0
NGINX_READY=0
while [ $attempt -le 59 ]; do
  attempt=$(( $attempt + 1 ))
  echo "Đợi nginx sẵn sàng (lần $attempt)..."
  result=$( (docker logs dauthau_reverse_proxy) 2>&1 )
  if grep -q 'Configuration complete; ready for start up' <<< $result ; then
    echo "Nginx sẵn sàng!"
    NGINX_READY=1
    break
  fi
  sleep 2
done
if [[ ! "$NGINX_READY" -eq 1 ]]; then
  echo "Không khởi chạy nginx thành công, vui lòng kiểm tra lại"
  exit
fi

# # Chờ es_dauthau chạy hoàn tất
# attempt=0
# ES1_READY=0
# while [ $attempt -le 59 ]; do
#   attempt=$(( $attempt + 1 ))
#   echo "Đợi ES DauThau sẵn sàng (lần $attempt)..."
#   result=$( (docker logs es_dauthau) 2>&1 )
#   if grep -q 'successfully reloaded changed geoip database file' <<< $result ; then
#     echo "ES DauThau sẵn sàng!"
#     ES1_READY=1
#     break
#   fi
#   sleep 2
# done
# if [[ ! "$ES1_READY" -eq 1 ]]; then
#   echo "Không khởi chạy ES DauThau thành công, vui lòng kiểm tra lại"
#   exit
# fi

# # Chờ es_dauthaunet chạy hoàn tất
# attempt=0
# ES1_READY=0
# while [ $attempt -le 59 ]; do
#   attempt=$(( $attempt + 1 ))
#   echo "Đợi ES Dtnet sẵn sàng (lần $attempt)..."
#   result=$( (docker logs es_dauthaunet) 2>&1 )
#   if grep -q 'successfully reloaded changed geoip database file' <<< $result ; then
#     echo "ES Dtnet sẵn sàng!"
#     ES1_READY=1
#     break
#   fi
#   sleep 2
# done
# if [[ ! "$ES1_READY" -eq 1 ]]; then
#   echo "Không khởi chạy ES Dtnet thành công, vui lòng kiểm tra lại"
#   exit
# fi

# Tạo và import các CSDL
arrDbs=("dauthau_2018" "dauthau_net" "dauthau_id" "dauthau_crawls" "dauthau_fileserver")
arrFileDbs=("dauthau_2018_sso.sql.gz" "dauthau_net_sso.sql.gz" "dauthau_id_sso.sql.gz" "dauthau_crawls.sql.gz" "dauthau_fileserver.sql")
arrDirDbs=("dauthau.info" "dauthau.net" "id.dauthau.net" "dauthau-crawls" "fileserver")

for i in ${!arrDbs[@]}; do
  dbname=${arrDbs[$i]}
  dbfile=${arrFileDbs[$i]}
  dbdir=${arrDirDbs[$i]}

  if [ ! -d "$DIR_PATH/_docker/mysql/$dbname" ] ; then
    echo "Tạo và import CSDL $dbname"
    if [ -f "$DIR_PATH/db/$dbfile" ] ; then
      rm -f "$DIR_PATH/db/$dbfile"
    fi
    cp "$DIR_PATH/src/$dbdir/vinades/$dbfile" "$DIR_PATH/db/$dbfile"
    docker exec dauthau_db bin/bash -c "mariadb -u root -e 'CREATE DATABASE $dbname;'"
    if [[ "$dbfile" =~ \.gz$ ]] ; then
      docker exec dauthau_db bin/bash -c "zcat /home/<USER>"
    else
      docker exec dauthau_db bin/bash -c "mariadb -u root $dbname < /home/<USER>"
    fi
    rm -f "$DIR_PATH/db/$dbfile"
  fi
done

# # Mapping DauThau.info Bidding nếu chưa có
# if ( (curl -sSX GET 'http://localhost:9200/_cat/indices/dauthau_bidding') 2>&1 ) | grep '"status":404' ; then
#   echo "ES DauThau.info Bidding chưa có dữ liệu, tạo mapping các index"
#   docker exec dauthau_sendmailaws bash -c "php /var/www/private/elastic_mappings_all.php;"
# fi

# # Mapping ES DauThau.Net Bid nếu chưa có
# if ( (curl -sSX GET 'http://localhost:9201/_cat/indices/bids_profile') 2>&1 ) | grep '"status":404' ; then
#   echo "ES DauThau.Net Bid chưa có dữ liệu, tạo mapping các index"
#   docker exec dauthau_netprivate bash -c "php /var/www/private/mappings.php;"
# fi

# # Mapping ES CRM nếu chưa có
# if ( (curl -sSX GET 'http://localhost:9201/_cat/indices/crm_leads') 2>&1 ) | grep '"status":404' ; then
#   echo "ES CRM chưa có dữ liệu, tạo mapping các index"
#   docker exec dauthau_crmprivate bash -c "php /var/www/private/elastic_crm_mappings.php;"
# fi

# # Mapping ES DauThau.info Mail net nếu chưa có
# if ( (curl -sSX GET 'http://localhost:9201/_cat/indices/dauthau_mail') 2>&1 ) | grep '"status":404' ; then
#   echo "ES DauThau.info Mail chưa có dữ liệu, tạo mapping các index"
#   docker exec dauthau_sendmailaws bash -c "php /var/www/private/elastic_dtnet_mappings.php;"
# fi

echo "Xong!"
